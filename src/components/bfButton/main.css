@charset "UTF-8";
/* BfButton 组件样式 */
.bf-button.clipped-el-button-wrapper {
  /* 1. 容器 */
  position: relative;
  display: inline-block;
  width: var(--button-width);
  height: var(--button-height);
  isolation: isolate;
  transition: transform 0.2s ease, filter 0.3s ease, box-shadow 0.3s ease;
  /* 2. 底层：渐变边框 */
  /* 3. 中层：背景 */
  /* 5. 内部的 el-button */
}

.bf-button.clipped-el-button-wrapper:active {
  transform: scale(0.98);
}

.bf-button.clipped-el-button-wrapper::before {
  content: '';
  position: absolute;
  inset: 0;
  z-index: -2;
  transition: filter 0.3s ease;
  clip-path: var(--clip-path-value);
}

.bf-button.clipped-el-button-wrapper::after {
  content: '';
  position: absolute;
  inset: var(--border-width);
  background: var(--background-color);
  z-index: -1;
  clip-path: var(--clip-path-value);
  box-shadow: inset 0 -4px 6px #FF4A4A2B;
}

.bf-button.clipped-el-button-wrapper .corner-triangle {
  position: absolute;
  width: 4px;
  height: 4px;
  background-color: var(--color);
  z-index: 1;
  transition: filter 0.3s ease;
}

.bf-button.clipped-el-button-wrapper .corner-triangle.top-left {
  top: 0;
  left: 0;
  clip-path: polygon(0 0, 100% 0, 0 100%);
}

.bf-button.clipped-el-button-wrapper .corner-triangle.bottom-right {
  bottom: 0;
  right: 0;
  clip-path: polygon(100% 100%, 100% 0, 0 100%);
}

.bf-button.clipped-el-button-wrapper:hover::before,
.bf-button.clipped-el-button-wrapper:hover .corner-triangle {
  filter: brightness(1.2);
}

.bf-button.clipped-el-button-wrapper :deep(.el-button) {
  background: transparent !important;
  border: none !important;
  color: var(--color) !important;
  font-size: var(--font-size) !important;
  font-family: 'Heiti SC', 'Microsoft YaHei', sans-serif !important;
  font-weight: bold !important;
  width: 100%;
  height: 100%;
  padding: 12px 40px;
}

.bf-button.clipped-el-button-wrapper :deep(.el-button):hover, .bf-button.clipped-el-button-wrapper :deep(.el-button):focus {
  background-color: transparent !important;
  border-color: transparent !important;
  color: var(--color) !important;
}
