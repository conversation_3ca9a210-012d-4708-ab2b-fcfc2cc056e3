<template>
  <div class="test-container">
    <h2>BfButton 组件测试</h2>
    
    <!-- 默认按钮 -->
    <div class="test-section">
      <h3>默认按钮</h3>
      <BfButton>默认按钮</BfButton>
    </div>
    
    <!-- 自定义样式按钮 -->
    <div class="test-section">
      <h3>自定义样式按钮</h3>
      <BfButton 
        width="200px" 
        height="60px" 
        fontSize="18px"
        backgroundColor="#0066CC2B"
        color="#00CCFF"
      >
        自定义按钮
      </BfButton>
    </div>
    
    <!-- 不同尺寸按钮 -->
    <div class="test-section">
      <h3>不同尺寸按钮</h3>
      <BfButton width="120px" height="40px" fontSize="14px">小按钮</BfButton>
      <BfButton width="160px" height="50px" fontSize="16px">中按钮</BfButton>
      <BfButton width="200px" height="60px" fontSize="18px">大按钮</BfButton>
    </div>
    
    <!-- 带事件的按钮 -->
    <div class="test-section">
      <h3>带事件的按钮</h3>
      <BfButton @click="handleClick">点击我</BfButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import BfButton from './main'

const handleClick = () => {
  alert('按钮被点击了！')
}
</script>

<style scoped>
.test-container {
  padding: 20px;
  background: #1a1a1a;
  color: white;
  min-height: 100vh;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h3 {
  margin-bottom: 15px;
  color: #00CCFF;
}

.test-section > * {
  margin-right: 15px;
  margin-bottom: 15px;
}
</style>
