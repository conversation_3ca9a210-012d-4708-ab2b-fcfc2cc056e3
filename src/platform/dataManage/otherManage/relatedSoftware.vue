<template>
  <div class="text-center mt-[16px] bg-black/40 h-[calc(100%_-_51px_-_40px)] related-software" v-html="softwareHtml" />
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue'
  import { useI18n } from 'vue-i18n'
  const { t } = useI18n()

  const REQUEST_SOFTWARE_INTERVAL = 10 * 60 * 1000 // 10 分钟
  const SOFTWARE_URL = '/bf8100/assets/software/'
  const softwareHtml = ref<string>('123123123123123')
  let timer: ReturnType<typeof setInterval> | null = null
  const getSoftwareHtml = async () => {
    console.log('Fetching related software HTML...')
    try {
      const response = await fetch(`${SOFTWARE_URL}?${Date.now()}`)

      if (!response.ok) {
        throw new Error(`Network response was not ok, status: ${response.status}`)
      }

      const htmlStr = await response.text()
      softwareHtml.value = htmlStr.replace(/href="/gi, `download href="${SOFTWARE_URL}`)
    } catch (error) {
      console.error(t('software.refreshFailed'), error)
    }
  }

  onMounted(() => {
    getSoftwareHtml()

    timer = setInterval(getSoftwareHtml, REQUEST_SOFTWARE_INTERVAL)
  })

  onUnmounted(() => {
    if (timer) {
      clearInterval(timer)
      console.log('RelatedSoftware timer cleared.')
    }
  })
</script>

<style lang="scss">
  .related-software {
    a {
      color: #007bff;
    }
  }
</style>
